{"name": "@synapseai/backend", "version": "1.0.0", "description": "SynapseAI Backend - Universal AI Orchestration System", "main": "dist/main.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:prod": "prisma migrate deploy", "db:studio": "prisma studio", "db:seed": "ts-node prisma/seed.ts"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-socket.io": "^10.0.0", "@nestjs/websockets": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/jwt": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/throttler": "^5.0.0", "@nestjs/bull": "^10.0.0", "@nestjs/schedule": "^4.0.0", "@nestjs/swagger": "^7.0.0", "@prisma/client": "^5.0.0", "prisma": "^5.0.0", "passport": "^0.6.0", "passport-jwt": "^4.0.0", "passport-local": "^1.0.0", "bcrypt": "^5.1.0", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "helmet": "^7.0.0", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "express-rate-limit": "^6.7.0", "socket.io": "^4.7.0", "redis": "^4.6.0", "ioredis": "^5.3.0", "bull": "^4.10.0", "rxjs": "^7.8.0", "winston": "^3.8.0", "nest-winston": "^1.9.0", "@sentry/node": "^7.50.0", "openai": "^4.0.0", "@anthropic-ai/sdk": "^0.57.0", "google-generativeai": "^0.1.0", "groq-sdk": "^0.3.0", "stripe": "^12.0.0", "nodemailer": "^6.9.0", "multer": "^1.4.5-lts.1", "sharp": "^0.32.0", "pdf-parse": "^1.1.1", "mammoth": "^1.5.1", "cheerio": "^1.0.0-rc.12", "axios": "^1.4.0", "uuid": "^9.0.0", "date-fns": "^2.30.0", "lodash": "^4.17.21", "reflect-metadata": "^0.1.13"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@types/bcrypt": "^5.0.0", "@types/passport-jwt": "^3.0.8", "@types/passport-local": "^1.0.35", "@types/multer": "^1.4.7", "@types/nodemailer": "^6.4.8", "@types/uuid": "^9.0.2", "@types/lodash": "^4.14.195", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.5.0", "prettier": "^2.8.8", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}